"""
Core configuration and settings for the Token Analyzer system.
Modern, type-safe configuration using Pydantic Settings.
"""

from enum import Enum
from functools import lru_cache
from pathlib import Path
from typing import Any

from pydantic import Field, SecretStr, validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class LogLevel(str, Enum):
    """Supported log levels."""

    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class Environment(str, Enum):
    """Application environments."""

    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class BlockchainNetwork(str, Enum):
    """Supported blockchain networks."""

    ETHEREUM = "ethereum"
    POLYGON = "polygon"
    BSC = "bsc"
    ARBITRUM = "arbitrum"
    OPTIMISM = "optimism"
    AVALANCHE = "avalanche"


class APISettings(BaseSettings):
    """API-related configuration."""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", env_prefix="API_", case_sensitive=False, extra="ignore"
    )

    # Blockchain APIs
    etherscan_api_key: SecretStr | None = Field(None, description="Etherscan API key")
    infura_project_id: SecretStr | None = Field(None, description="Infura project ID")
    alchemy_api_key: SecretStr | None = Field(None, description="Alchemy API key")

    # Exchange APIs
    coinbase_api_key: SecretStr | None = Field(None, description="Coinbase Pro API key")
    coinbase_api_secret: SecretStr | None = Field(
        None, description="Coinbase Pro secret"
    )
    coinbase_passphrase: SecretStr | None = Field(
        None, description="Coinbase Pro passphrase"
    )

    # LLM APIs
    openrouter_api_key: SecretStr | None = Field(None, description="OpenRouter API key")
    anthropic_api_key: SecretStr | None = Field(None, description="Anthropic API key")

    # Token Discovery APIs
    birdeye_api_key: SecretStr | None = Field(None, description="Birdeye API key")

    # Rate limiting
    default_rate_limit: int = Field(100, description="Default requests per minute")
    burst_rate_limit: int = Field(200, description="Burst requests per minute")

    # Timeouts
    request_timeout: float = Field(30.0, description="Request timeout in seconds")
    connection_timeout: float = Field(10.0, description="Connection timeout in seconds")


class DatabaseSettings(BaseSettings):
    """Database configuration."""

    model_config = SettingsConfigDict(
        env_prefix="DB_", case_sensitive=False, extra="ignore"
    )

    # DuckDB
    duckdb_path: Path = Field(
        Path("./data/analytics.db"), description="DuckDB database file path"
    )
    duckdb_memory_limit: str = Field("2GB", description="DuckDB memory limit")
    duckdb_threads: int = Field(4, description="DuckDB thread count")

    # Redis
    redis_url: str = Field("redis://localhost:6379", description="Redis connection URL")
    redis_password: SecretStr | None = Field(None, description="Redis password")
    redis_db: int = Field(0, description="Redis database number")
    redis_max_connections: int = Field(20, description="Redis connection pool size")

    # Cache settings
    cache_ttl_default: int = Field(300, description="Default cache TTL in seconds")
    cache_ttl_market_data: int = Field(60, description="Market data cache TTL")
    cache_ttl_chain_data: int = Field(3600, description="Chain data cache TTL")


class SystemSettings(BaseSettings):
    """System-wide configuration."""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore"
    )

    # Environment
    environment: Environment = Field(
        Environment.DEVELOPMENT, description="Application environment"
    )
    debug: bool = Field(False, description="Enable debug mode")

    # Logging
    log_level: LogLevel = Field(LogLevel.INFO, description="Logging level")
    log_format: str = Field("json", description="Log format (json/text)")
    log_file: Path | None = Field(None, description="Log file path")

    # Application
    app_name: str = Field("Token Analyzer", description="Application name")
    app_version: str = Field("1.0.0", description="Application version")

    # MCP Server
    mcp_server_name: str = Field("TokenAnalyzer", description="MCP server name")
    mcp_server_port: int = Field(8000, description="MCP server port")
    mcp_server_host: str = Field("0.0.0.0", description="MCP server host")

    # Data directories
    data_dir: Path = Field(Path("./data"), description="Data directory")
    cache_dir: Path = Field(Path("./cache"), description="Cache directory")
    logs_dir: Path = Field(Path("./logs"), description="Logs directory")

    # Processing
    max_concurrent_requests: int = Field(10, description="Max concurrent API requests")
    max_workers: int = Field(4, description="Max worker threads")
    batch_size: int = Field(50, description="Batch processing size")

    # Scheduler
    scheduler_interval: int = Field(900, description="Scheduler interval in seconds")
    enable_scheduler: bool = Field(True, description="Enable background scheduler")

    @validator("data_dir", "cache_dir", "logs_dir", pre=True)
    def create_directories(cls, v):
        """Ensure directories exist."""
        path = Path(v)
        path.mkdir(parents=True, exist_ok=True)
        return path


class ValidationSettings(BaseSettings):
    """Token validation criteria."""

    model_config = SettingsConfigDict(
        env_prefix="VALIDATION_", case_sensitive=False, extra="ignore"
    )

    # Liquidity requirements
    min_liquidity_usd: float = Field(100_000.0, description="Minimum liquidity in USD")
    min_volume_24h_usd: float = Field(50_000.0, description="Minimum 24h volume in USD")

    # Age requirements
    min_age_days: int = Field(7, description="Minimum token age in days")
    max_age_days: int = Field(365, description="Maximum token age in days")

    # Market metrics
    min_market_cap_usd: float = Field(
        1_000_000.0, description="Minimum market cap in USD"
    )
    max_volatility_24h: float = Field(0.5, description="Maximum 24h volatility (0-1)")

    # Social metrics
    min_fear_greed_index: int = Field(20, description="Minimum fear & greed index")
    min_social_mentions: int = Field(10, description="Minimum social mentions")

    # Contract validation
    require_verified_contract: bool = Field(
        True, description="Require verified contracts"
    )
    require_audit: bool = Field(False, description="Require security audit")
    blacklisted_contracts: list[str] = Field(
        default_factory=list, description="Blacklisted contract addresses"
    )


class MonitoringSettings(BaseSettings):
    """Monitoring and observability configuration."""

    model_config = SettingsConfigDict(
        env_prefix="MONITORING_", case_sensitive=False, extra="ignore"
    )

    # Metrics
    enable_metrics: bool = Field(True, description="Enable metrics collection")
    metrics_port: int = Field(9090, description="Metrics server port")
    metrics_endpoint: str = Field("/metrics", description="Metrics endpoint path")

    # Health checks
    health_check_interval: int = Field(
        30, description="Health check interval in seconds"
    )
    health_check_timeout: float = Field(5.0, description="Health check timeout")

    # Alerting
    enable_alerts: bool = Field(False, description="Enable alerting")
    alert_webhook_url: str | None = Field(None, description="Webhook URL for alerts")

    # Tracing
    enable_tracing: bool = Field(False, description="Enable distributed tracing")
    jaeger_endpoint: str | None = Field(None, description="Jaeger collector endpoint")


class BlockchainSettings(BaseSettings):
    """Blockchain RPC and Web3 provider configuration."""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore"
    )

    # Main RPC URLs (from .env file)
    ethereum_rpc_url: str | None = Field(None, description="Ethereum RPC URL")
    polygon_rpc_url: str | None = Field(None, description="Polygon RPC URL")
    bsc_rpc_url: str | None = Field(None, description="BSC RPC URL")
    arbitrum_rpc_url: str | None = Field(None, description="Arbitrum RPC URL")
    optimism_rpc_url: str | None = Field(None, description="Optimism RPC URL")
    avalanche_rpc_url: str | None = Field(None, description="Avalanche RPC URL")
    fantom_rpc_url: str | None = Field(None, description="Fantom RPC URL")

    # Web3 Provider URLs (from .env file)
    web3_provider_url: str | None = Field(None, description="Primary Web3 provider URL")

    # Infura settings
    infura_api_key: str | None = Field(None, description="Infura API key")
    infura_mainnet_ws: str | None = Field(None, description="Infura mainnet WebSocket")
    infura_sepolia_ws: str | None = Field(None, description="Infura Sepolia WebSocket")

    # API Keys for blockchain services
    etherscan_api_key: str | None = Field(None, description="Etherscan API key")
    dune_api_key: str | None = Field(None, description="Dune Analytics API key")

    # CoinGecko and market data
    coingecko_api_key: str | None = Field(None, description="CoinGecko API key")
    coin_api_key: str | None = Field(None, description="CoinAPI key")

    # Social Media APIs (for sentiment analysis)
    twitter_bearer_token: str | None = Field(None, description="Twitter Bearer Token")
    reddit_client_id: str | None = Field(None, description="Reddit Client ID")
    reddit_client_secret: str | None = Field(None, description="Reddit Client Secret")


class Config:
    """Main configuration container."""

    def __init__(self):
        self.system = SystemSettings()
        self.api = APISettings()
        self.database = DatabaseSettings()
        self.validation = ValidationSettings()
        self.monitoring = MonitoringSettings()
        self.blockchain = BlockchainSettings()

        # Ensure required directories exist
        self._setup_directories()

    def _setup_directories(self):
        """Create required directories."""
        for dir_path in [
            self.system.data_dir,
            self.system.cache_dir,
            self.system.logs_dir,
        ]:
            dir_path.mkdir(parents=True, exist_ok=True)

    @property
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.system.environment == Environment.DEVELOPMENT

    @property
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return self.system.environment == Environment.PRODUCTION

    def get_web3_config(self) -> dict[str, Any]:
        """Get Web3 provider configuration."""
        config = {}

        if self.api.infura_project_id:
            config["infura"] = {
                "project_id": self.api.infura_project_id.get_secret_value(),
                "endpoint": "https://mainnet.infura.io/v3/{project_id}",
            }

        if self.api.alchemy_api_key:
            config["alchemy"] = {
                "api_key": self.api.alchemy_api_key.get_secret_value(),
                "endpoint": "https://eth-mainnet.g.alchemy.com/v2/{api_key}",
            }

        return config

    def get_redis_config(self) -> dict[str, Any]:
        """Get Redis configuration."""
        return {
            "url": self.database.redis_url,
            "password": self.database.redis_password.get_secret_value()
            if self.database.redis_password
            else None,
            "db": self.database.redis_db,
            "max_connections": self.database.redis_max_connections,
            "encoding": "utf-8",
            "decode_responses": True,
        }

    # Backward compatibility properties for agents expecting uppercase attributes
    @property
    def ETHEREUM_RPC_URL(self) -> str | None:
        """Get Ethereum RPC URL."""
        return self.blockchain.ethereum_rpc_url

    @property
    def BSC_RPC_URL(self) -> str | None:
        """Get BSC RPC URL."""
        return self.blockchain.bsc_rpc_url

    @property
    def POLYGON_RPC_URL(self) -> str | None:
        """Get Polygon RPC URL."""
        return self.blockchain.polygon_rpc_url

    @property
    def ARBITRUM_RPC_URL(self) -> str | None:
        """Get Arbitrum RPC URL."""
        return self.blockchain.arbitrum_rpc_url

    @property
    def OPTIMISM_RPC_URL(self) -> str | None:
        """Get Optimism RPC URL."""
        return self.blockchain.optimism_rpc_url

    @property
    def AVALANCHE_RPC_URL(self) -> str | None:
        """Get Avalanche RPC URL."""
        return self.blockchain.avalanche_rpc_url

    @property
    def FANTOM_RPC_URL(self) -> str | None:
        """Get Fantom RPC URL."""
        return self.blockchain.fantom_rpc_url

    @property
    def TWITTER_BEARER_TOKEN(self) -> str | None:
        """Get Twitter Bearer Token."""
        return self.blockchain.twitter_bearer_token

    @property
    def REDDIT_CLIENT_ID(self) -> str | None:
        """Get Reddit Client ID."""
        return self.blockchain.reddit_client_id

    @property
    def REDDIT_CLIENT_SECRET(self) -> str | None:
        """Get Reddit Client Secret."""
        return self.blockchain.reddit_client_secret


@lru_cache
def get_config() -> Config:
    """Get cached configuration instance."""
    return Config()


@lru_cache
def get_settings() -> Config:
    """Get cached settings instance (alias for get_config for compatibility)."""
    return get_config()


# Export main config instance
config = get_config()
settings = get_settings()
